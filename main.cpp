#include <iostream>
#include <vector>
#include <algorithm>
#include <cmath>
#include <climits>

using namespace std;

struct Server {
    int npu_count;
    int speed_coeff;
    int memory;
};

struct User {
    int start_time;
    int end_time;
    int sample_count;
    vector<int> latency; // latency to each server
};

struct Request {
    int time;
    int server;
    int npu;
    int batch_size;
};

int main() {
    ios::sync_with_stdio(false);
    cin.tie(nullptr);
    
    int N;
    cin >> N;
    
    vector<Server> servers(N);
    for (int i = 0; i < N; i++) {
        cin >> servers[i].npu_count >> servers[i].speed_coeff >> servers[i].memory;
    }
    
    int M;
    cin >> M;
    
    vector<User> users(M);
    for (int i = 0; i < M; i++) {
        cin >> users[i].start_time >> users[i].end_time >> users[i].sample_count;
        users[i].latency.resize(N);
    }
    
    // Read latency matrix
    for (int i = 0; i < N; i++) {
        for (int j = 0; j < M; j++) {
            cin >> users[j].latency[i];
        }
    }
    
    int a, b;
    cin >> a >> b;
    
    // Generate solution for each user
    for (int user_id = 0; user_id < M; user_id++) {
        vector<Request> requests;
        
        int remaining_samples = users[user_id].sample_count;
        int current_time = users[user_id].start_time;
        
        // Find best server for this user (minimize latency and maximize efficiency)
        int best_server = 0;
        int min_latency = users[user_id].latency[0];
        for (int s = 1; s < N; s++) {
            if (users[user_id].latency[s] < min_latency) {
                min_latency = users[user_id].latency[s];
                best_server = s;
            }
        }
        
        // Calculate maximum batch size that fits in memory
        int max_batch_size = (servers[best_server].memory - b) / a;
        max_batch_size = min(max_batch_size, 1000); // constraint from problem
        max_batch_size = max(max_batch_size, 1); // at least 1
        
        // Use NPU 1 to avoid migration penalty
        int target_npu = 1;
        
        // Distribute samples across time to meet deadline
        int time_window = users[user_id].end_time - users[user_id].start_time;
        int min_interval = users[user_id].latency[best_server] + 1;
        
        while (remaining_samples > 0) {
            // Calculate optimal batch size
            int batch_size = min(remaining_samples, max_batch_size);
            
            // Ensure we don't exceed time constraints
            int send_time = current_time;
            int arrival_time = send_time + users[user_id].latency[best_server];
            
            // Calculate processing time
            double speed = servers[best_server].speed_coeff * sqrt(batch_size);
            int process_time = (int)ceil(batch_size / speed);
            int completion_time = arrival_time + process_time;
            
            // Check if we can meet deadline
            if (completion_time > users[user_id].end_time) {
                // Reduce batch size if needed
                batch_size = min(batch_size, remaining_samples);
                if (batch_size <= 0) batch_size = 1;
            }
            
            requests.push_back({send_time, best_server + 1, target_npu, batch_size});
            
            remaining_samples -= batch_size;
            current_time = send_time + min_interval;
            
            // Prevent infinite loop
            if (requests.size() >= 300) break;
        }
        
        // Output for this user
        cout << requests.size() << "\n";
        for (const auto& req : requests) {
            cout << req.time << " " << req.server << " " << req.npu << " " << req.batch_size;
            if (&req != &requests.back()) cout << " ";
        }
        cout << "\n";
    }
    
    return 0;
}

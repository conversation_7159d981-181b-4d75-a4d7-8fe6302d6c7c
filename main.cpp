#include <iostream>
#include <vector>
#include <algorithm>
#include <cmath>
#include <climits>

using namespace std;

struct Server {
    int npu_count;
    int speed_coeff;
    int memory;
};

struct User {
    int start_time;
    int end_time;
    int sample_count;
    vector<int> latency; // latency to each server
};

struct Request {
    int time;
    int server;
    int npu;
    int batch_size;
};

// Calculate optimal batch size for given server and remaining samples
int calculateOptimalBatchSize(const Server& server, int remaining_samples, int a, int b) {
    int max_memory_batch = (server.memory - b) / a;
    max_memory_batch = min(max_memory_batch, 1000);
    max_memory_batch = max(max_memory_batch, 1);

    // Find batch size that maximizes efficiency (samples per unit time)
    int optimal_batch = 1;
    double best_efficiency = 0.0;

    for (int batch = 1; batch <= min(remaining_samples, max_memory_batch); batch++) {
        double speed = server.speed_coeff * sqrt(batch);
        double process_time = ceil(batch / speed);
        double efficiency = batch / process_time;

        if (efficiency > best_efficiency) {
            best_efficiency = efficiency;
            optimal_batch = batch;
        }
    }

    return optimal_batch;
}

// Calculate server score considering latency and performance
double calculateServerScore(const Server& server, int user_latency, int remaining_samples, int a, int b) {
    int optimal_batch = calculateOptimalBatchSize(server, remaining_samples, a, b);
    double speed = server.speed_coeff * sqrt(optimal_batch);
    double process_time = ceil(optimal_batch / speed);
    double total_time = user_latency + process_time;

    // Score = efficiency / total_time (higher is better)
    return optimal_batch / total_time;
}

int main() {
    ios::sync_with_stdio(false);
    cin.tie(nullptr);

    int N;
    cin >> N;

    vector<Server> servers(N);
    for (int i = 0; i < N; i++) {
        cin >> servers[i].npu_count >> servers[i].speed_coeff >> servers[i].memory;
    }

    int M;
    cin >> M;

    vector<User> users(M);
    for (int i = 0; i < M; i++) {
        cin >> users[i].start_time >> users[i].end_time >> users[i].sample_count;
        users[i].latency.resize(N);
    }

    // Read latency matrix
    for (int i = 0; i < N; i++) {
        for (int j = 0; j < M; j++) {
            cin >> users[j].latency[i];
        }
    }

    int a, b;
    cin >> a >> b;
    
    // Generate solution for each user
    for (int user_id = 0; user_id < M; user_id++) {
        vector<Request> requests;

        int remaining_samples = users[user_id].sample_count;
        int current_time = users[user_id].start_time;

        // Find best server for this user considering both latency and performance
        int best_server = 0;
        double best_score = calculateServerScore(servers[0], users[user_id].latency[0], remaining_samples, a, b);

        for (int s = 1; s < N; s++) {
            double score = calculateServerScore(servers[s], users[user_id].latency[s], remaining_samples, a, b);
            if (score > best_score) {
                best_score = score;
                best_server = s;
            }
        }

        // Use NPU 1 to minimize migration penalty
        int target_npu = 1;

        // Calculate time constraints
        int time_window = users[user_id].end_time - users[user_id].start_time;
        int min_send_interval = users[user_id].latency[best_server] + 1;

        // Ensure we have enough time to send all requests
        int max_requests = min(300, time_window / min_send_interval + 1);

        while (remaining_samples > 0 && requests.size() < max_requests) {
            // Calculate optimal batch size for current situation
            int batch_size = calculateOptimalBatchSize(servers[best_server], remaining_samples, a, b);

            // Ensure we don't exceed time constraints
            int send_time = current_time;
            int arrival_time = send_time + users[user_id].latency[best_server];

            // Calculate processing time
            double speed = servers[best_server].speed_coeff * sqrt(batch_size);
            int process_time = (int)ceil(batch_size / speed);
            int completion_time = arrival_time + process_time;

            // Adjust batch size if we're running out of time
            int remaining_time = users[user_id].end_time - arrival_time;
            if (process_time > remaining_time && remaining_time > 0) {
                // Try smaller batch sizes to fit in remaining time
                for (int test_batch = batch_size - 1; test_batch >= 1; test_batch--) {
                    if (test_batch * a + b <= servers[best_server].memory) {
                        double test_speed = servers[best_server].speed_coeff * sqrt(test_batch);
                        int test_process_time = (int)ceil(test_batch / test_speed);
                        if (test_process_time <= remaining_time) {
                            batch_size = test_batch;
                            break;
                        }
                    }
                }
            }

            // Ensure batch size is valid
            batch_size = min(batch_size, remaining_samples);
            batch_size = max(batch_size, 1);

            // Verify memory constraint
            if (batch_size * a + b > servers[best_server].memory) {
                batch_size = (servers[best_server].memory - b) / a;
                batch_size = max(batch_size, 1);
            }

            requests.push_back({send_time, best_server + 1, target_npu, batch_size});

            remaining_samples -= batch_size;
            current_time = send_time + min_send_interval;

            // Break if we can't send more requests in time
            if (current_time >= users[user_id].end_time - users[user_id].latency[best_server]) {
                break;
            }
        }

        // If we still have remaining samples, try to fit them in the last request
        if (remaining_samples > 0 && !requests.empty()) {
            int last_idx = requests.size() - 1;
            int additional_batch = min(remaining_samples,
                (servers[best_server].memory - b) / a - requests[last_idx].batch_size);
            if (additional_batch > 0) {
                requests[last_idx].batch_size += additional_batch;
                remaining_samples -= additional_batch;
            }
        }

        // Output for this user
        cout << requests.size() << "\n";
        for (const auto& req : requests) {
            cout << req.time << " " << req.server << " " << req.npu << " " << req.batch_size;
            if (&req != &requests.back()) cout << " ";
        }
        cout << "\n";
    }
    
    return 0;
}
